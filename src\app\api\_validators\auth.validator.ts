import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { errorResponse } from '@/utils/response';
import { ErrorCode } from '@/constants/error-codes';

// Key authentication request schema
export const keyAuthSchema = z.object({
  accessKey: z.string()
    .min(1, 'Access key is required')
    .regex(/^[a-z]+-[a-z]+-\d{2}$/, 'Invalid access key format. Expected format: word-word-number'),
});

// Register request schema (no fields needed - system generates key)
export const registerSchema = z.object({});

// Key regeneration request schema
export const keyRegenerationSchema = z.object({
  currentAccessKey: z.string()
    .min(1, 'Current access key is required')
    .regex(/^[a-z]+-[a-z]+-\d{2}$/, 'Invalid access key format'),
});

/**
 * Validate key authentication request
 */
export async function validateKeyAuth(req: NextRequest): Promise<{ data: z.infer<typeof keyAuthSchema> } | NextResponse> {
  try {
    const body = await req.json();
    const result = keyAuthSchema.safeParse(body);

    if (!result.success) {
      const errors = result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');
      return errorResponse(errors, ErrorCode.VALIDATION_ERROR, 400);
    }

    return { data: result.data };
  } catch (error) {
    return errorResponse('Invalid request body', ErrorCode.BAD_REQUEST, 400);
  }
}

/**
 * Validate register request
 */
export async function validateRegister(req: NextRequest): Promise<{ data: z.infer<typeof registerSchema> } | NextResponse> {
  try {
    // For registration, we don't need any body data since the system generates the key
    // But we still need to parse the body to ensure it's valid JSON
    try {
      await req.json();
    } catch {
      // If no body or invalid JSON, that's fine for registration
    }

    const result = registerSchema.safeParse({});

    if (!result.success) {
      const errors = result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');
      return errorResponse(errors, ErrorCode.VALIDATION_ERROR, 400);
    }

    return { data: result.data };
  } catch (error) {
    return errorResponse('Invalid request', ErrorCode.BAD_REQUEST, 400);
  }
}

/**
 * Validate key regeneration request
 */
export async function validateKeyRegeneration(req: NextRequest): Promise<{ data: z.infer<typeof keyRegenerationSchema> } | NextResponse> {
  try {
    const body = await req.json();
    const result = keyRegenerationSchema.safeParse(body);

    if (!result.success) {
      const errors = result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');
      return errorResponse(errors, ErrorCode.VALIDATION_ERROR, 400);
    }

    return { data: result.data };
  } catch (error) {
    return errorResponse('Invalid request body', ErrorCode.BAD_REQUEST, 400);
  }
}
