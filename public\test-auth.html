<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Authentication System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
            width: 200px;
        }
    </style>
</head>
<body>
    <h1>🔐 Authentication System Test</h1>
    
    <div class="container">
        <h2>1. Generate Access Key</h2>
        <button onclick="generateKey()">Generate New Key</button>
        <div id="generateResult" class="result"></div>
    </div>

    <div class="container">
        <h2>2. Save Access Key</h2>
        <input type="text" id="keyToSave" placeholder="Access key to save">
        <input type="text" id="userName" placeholder="User name (optional)">
        <button onclick="saveKey()">Save Key</button>
        <div id="saveResult" class="result"></div>
    </div>

    <div class="container">
        <h2>3. Login with Access Key</h2>
        <input type="text" id="loginKey" placeholder="Access key for login">
        <button onclick="loginWithKey()">Login</button>
        <div id="loginResult" class="result"></div>
    </div>

    <div class="container">
        <h2>4. Test Profile Update</h2>
        <input type="text" id="newName" placeholder="New name">
        <button onclick="updateProfile()" id="updateBtn" disabled>Update Profile</button>
        <div id="profileResult" class="result"></div>
    </div>

    <script>
        let authToken = null;

        async function generateKey() {
            const resultDiv = document.getElementById('generateResult');
            resultDiv.textContent = 'Generating key...';
            
            try {
                const response = await fetch('/api/auth/generate-key', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Generated key: ${data.data.accessKey}`;
                    document.getElementById('keyToSave').value = data.data.accessKey;
                    document.getElementById('loginKey').value = data.data.accessKey;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Error: ${data.error?.message || data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Network error: ${error.message}`;
            }
        }

        async function saveKey() {
            const resultDiv = document.getElementById('saveResult');
            const accessKey = document.getElementById('keyToSave').value;
            const name = document.getElementById('userName').value;
            
            if (!accessKey) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ Please enter an access key';
                return;
            }
            
            resultDiv.textContent = 'Saving key...';
            
            try {
                const response = await fetch('/api/auth/save-key', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        accessKey: accessKey,
                        name: name || '',
                    }),
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Key saved successfully: ${data.data.message}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Error: ${data.error?.message || data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Network error: ${error.message}`;
            }
        }

        async function loginWithKey() {
            const resultDiv = document.getElementById('loginResult');
            const accessKey = document.getElementById('loginKey').value;
            
            if (!accessKey) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ Please enter an access key';
                return;
            }
            
            resultDiv.textContent = 'Logging in...';
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        accessKey: accessKey,
                    }),
                });

                const data = await response.json();
                
                if (response.ok) {
                    authToken = data.data.token;
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Login successful!\nUser ID: ${data.data.user.id}\nName: ${data.data.user.name || 'Not set'}\nAccess Key: ${data.data.user.accessKey}`;
                    document.getElementById('updateBtn').disabled = false;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Error: ${data.error?.message || data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Network error: ${error.message}`;
            }
        }

        async function updateProfile() {
            const resultDiv = document.getElementById('profileResult');
            const newName = document.getElementById('newName').value;
            
            if (!authToken) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ Please login first';
                return;
            }
            
            resultDiv.textContent = 'Updating profile...';
            
            try {
                const response = await fetch('/api/auth/profile', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`,
                    },
                    body: JSON.stringify({
                        name: newName,
                    }),
                    credentials: 'include',
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Profile updated!\nName: ${data.data.name || 'Not set'}\nAccess Key: ${data.data.accessKey}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Error: ${data.error?.message || data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Network error: ${error.message}`;
            }
        }
    </script>
</body>
</html>
