"use client"

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertCircle, LayoutDashboard, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { Alert, AlertDescription } from '@/components/ui/alert';

const keyAuthSchema = z.object({
  accessKey: z.string()
    .min(1, { message: 'Access key is required' })
    .regex(/^[a-z]+-[a-z]+-\d{2}$/, { message: 'Invalid access key format. Expected: word-word-number (e.g., swift-river-42)' }),
});

export default function LoginPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [errorDetails, setErrorDetails] = useState<string | null>(null);
  const router = useRouter();

  const form = useForm<z.infer<typeof keyAuthSchema>>({
    resolver: zodResolver(keyAuthSchema),
    defaultValues: {
      accessKey: '',
    },
  });



  async function onSubmit(values: z.infer<typeof keyAuthSchema>) {
    setIsLoading(true);
    setErrorDetails(null);

    try {
      // Make the authentication request
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          accessKey: values.accessKey.toLowerCase().trim()
        }),
        credentials: 'include',
      });

      // Get the response text
      const responseText = await response.text();

      // Parse the JSON response
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (e) {
        setErrorDetails('Invalid response from server. Please try again later.');
        throw new Error('Server returned an invalid response');
      }

      // Check if login was successful
      if (!response.ok || !data.success) {
        const errorMessage = data.error?.message || 'Unknown error';
        const errorCode = data.error?.code || 'UNKNOWN_ERROR';

        // Provide user-friendly error messages
        if (errorCode === 'INVALID_CREDENTIALS' || errorCode === 'USER_NOT_FOUND') {
          setErrorDetails('Invalid access key. Please check your key and try again.');
          throw new Error('Invalid access key');
        } else if (errorCode === 'DATABASE_ERROR') {
          setErrorDetails('Database connection error. Please try again later.');
          throw new Error('Database connection failed');
        } else {
          setErrorDetails(`Authentication failed: ${errorMessage}`);
          throw new Error(errorMessage || 'Authentication failed');
        }
      }

      // Extract user data and token
      const responseData = data.data || {};
      const token = responseData.token;
      const user = responseData.user;

      if (!token || !user) {
        setErrorDetails('Server response is missing required data');
        throw new Error('Invalid server response');
      }

      // Store in localStorage for PWA support and offline access
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify({
        id: user.id,
        name: user.name,
        email: user.email,
        lastLogin: new Date().toISOString()
      }));

      // Also store the complete profile
      localStorage.setItem('userProfile', JSON.stringify(user));

      // --- Fix: Clear categories cache/localStorage so next page fetches fresh categories ---
      localStorage.removeItem('categories');
      // --------------------------------------------------------------

      toast.success('Logged in successfully');
      router.push('/home');
    } catch (error: any) {
      toast.error(error.message || 'Login failed');
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <Link href="/" className="absolute left-4 top-4 md:left-8 md:top-8 flex items-center gap-2 font-bold">
        <LayoutDashboard className="h-5 w-5" />
        <span>NoteHour</span>
      </Link>

      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl">Access Your Account</CardTitle>
          <CardDescription>
            Enter your access key to sign in
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              {errorDetails && (
                <Alert variant="destructive" className="mb-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="mt-2">
                    <div className="text-sm font-medium">Error Details:</div>
                    <div className="text-xs mt-1 break-words">{errorDetails}</div>
                  </AlertDescription>
                </Alert>
              )}

              <FormField
                control={form.control}
                name="accessKey"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Access Key</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="swift-river-42"
                        {...field}
                        className="font-mono"
                        autoComplete="off"
                      />
                    </FormControl>
                    <FormMessage />
                    <div className="text-xs text-muted-foreground mt-1">
                      Format: word-word-number (e.g., swift-river-42)
                    </div>
                  </FormItem>
                )}
              />

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Please wait
                  </>
                ) : (
                  'Sign In'
                )}
              </Button>

              <div className="text-xs text-center text-muted-foreground mt-2">
                If you are having trouble logging in, please make sure your email and password are correct.
              </div>
            </form>
          </Form>
        </CardContent>
        <CardFooter className="flex flex-col items-center gap-2">
          <div className="text-sm text-muted-foreground">
            Need an access key?{' '}
            <Link href="/auth/register" className="font-medium text-primary underline-offset-4 hover:underline">
              Generate Key
            </Link>
          </div>


        </CardFooter>
      </Card>
    </div>
  );
}
