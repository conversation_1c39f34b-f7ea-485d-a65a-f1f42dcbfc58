import { NextRequest } from 'next/server';
import * as jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { JWT_SECRET } from '@/config/auth';
import { JwtPayload, AuthResult } from '@/types/auth';
import { ApiError } from '@/core/errors/api-error';
import { ErrorCode } from '@/constants/error-codes';

/**
 * Get JWT token from request
 * @param req Next.js request
 * @returns JWT token or null
 */
export function getToken(req: NextRequest): string | null {
  // Try to get token from Authorization header
  const authHeader = req.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // If not in header, try cookies from the request
  return req.cookies.get('token')?.value || null;
}

/**
 * Verify authentication
 * @param req Next.js request
 * @returns Authentication result
 */
export function verifyAuth(req: NextRequest): AuthResult {
  try {
    const token = getToken(req);

    if (!token) {
      return { authenticated: false };
    }

    const decoded = (jwt as any).verify(token, JWT_SECRET) as JwtPayload;
    return {
      authenticated: true,
      user: decoded
    };
  } catch (error) {
    // Check error type by name since instanceof might not work with the import style
    if (error instanceof Error) {
      if (error.name === 'TokenExpiredError') {
        throw ApiError.unauthorized('Token expired', ErrorCode.TOKEN_EXPIRED);
      } else if (error.name === 'JsonWebTokenError') {
        throw ApiError.unauthorized('Invalid token', ErrorCode.INVALID_TOKEN);
      }
    }

    return { authenticated: false };
  }
}

/**
 * Generate JWT token
 * @param payload Token payload
 * @param expiresIn Token expiration time
 * @returns JWT token
 */
export function generateToken(payload: JwtPayload, expiresIn: string): string {
  // Use any type to bypass TypeScript checking
  return (jwt as any).sign(payload, JWT_SECRET, { expiresIn });
}

// Word lists for generating memorable access keys
const ADJECTIVES = [
  'swift', 'bright', 'calm', 'bold', 'wise', 'kind', 'pure', 'wild', 'free', 'true',
  'deep', 'warm', 'cool', 'soft', 'hard', 'fast', 'slow', 'high', 'low', 'new',
  'old', 'big', 'small', 'long', 'short', 'wide', 'thin', 'thick', 'light', 'dark',
  'red', 'blue', 'green', 'gold', 'silver', 'black', 'white', 'clear', 'sharp', 'smooth',
  'rough', 'gentle', 'strong', 'weak', 'young', 'fresh', 'dry', 'wet', 'hot', 'cold'
];

const NOUNS = [
  'river', 'mountain', 'ocean', 'forest', 'desert', 'valley', 'peak', 'lake', 'stream', 'hill',
  'cloud', 'star', 'moon', 'sun', 'wind', 'rain', 'snow', 'fire', 'earth', 'sky',
  'tree', 'flower', 'grass', 'stone', 'rock', 'sand', 'wave', 'tide', 'dawn', 'dusk',
  'bird', 'fish', 'wolf', 'bear', 'deer', 'eagle', 'hawk', 'owl', 'fox', 'lion',
  'tiger', 'whale', 'shark', 'dolphin', 'turtle', 'rabbit', 'horse', 'cat', 'dog', 'mouse'
];

/**
 * Generate a cryptographically secure random access key
 * Format: adjective-noun-number (e.g., 'swift-river-42')
 * Provides approximately 50 * 50 * 100 = 250,000 combinations
 * Plus additional entropy from crypto.randomBytes for the number
 * @returns Unique access key
 */
export function generateAccessKey(): string {
  // Use crypto.randomBytes for cryptographically secure randomness
  const randomBytes = crypto.randomBytes(4);

  // Generate indices for word selection
  const adjIndex = randomBytes[0] % ADJECTIVES.length;
  const nounIndex = randomBytes[1] % NOUNS.length;

  // Generate a 2-digit number (10-99) for additional entropy
  const number = 10 + (randomBytes[2] % 90);

  // Additional entropy: use the 4th byte to add more randomness to the number
  const extraEntropy = randomBytes[3] % 100;
  const finalNumber = (number + extraEntropy) % 100;
  const paddedNumber = finalNumber.toString().padStart(2, '0');

  return `${ADJECTIVES[adjIndex]}-${NOUNS[nounIndex]}-${paddedNumber}`;
}

/**
 * Validate access key format
 * @param key Access key to validate
 * @returns True if valid format
 */
export function isValidAccessKeyFormat(key: string): boolean {
  if (!key || typeof key !== 'string') return false;

  const parts = key.split('-');
  if (parts.length !== 3) return false;

  const [adjective, noun, number] = parts;

  // Check if adjective and noun are in our word lists
  const isValidAdjective = ADJECTIVES.includes(adjective.toLowerCase());
  const isValidNoun = NOUNS.includes(noun.toLowerCase());

  // Check if number is 2 digits
  const isValidNumber = /^\d{2}$/.test(number);

  return isValidAdjective && isValidNoun && isValidNumber;
}
