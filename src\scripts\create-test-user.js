/**
 * Create a test user in the database
 * Run with: node src/scripts/create-test-user.js <email> <password> <name>
 */

const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const path = require('path');
const fs = require('fs');

// Load environment variables from .env file
const envPath = path.join(__dirname, '../../.env');
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');
  envLines.forEach(line => {
    const [key, value] = line.split(' = ');
    if (key && value) {
      process.env[key.trim()] = value.trim();
    }
  });
}

// MongoDB connection string - update this with your actual connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/time-tracker-app';
console.log('Using MongoDB URI:', MONGODB_URI.replace(/\/\/[^:]+:[^@]+@/, '//***:***@'));

async function createTestUser(email, password, name) {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB successfully');
    
    // Check if the users collection exists, create it if not
    const collections = await mongoose.connection.db.listCollections({ name: 'users' }).toArray();
    if (collections.length === 0) {
      console.log('Users collection does not exist, will be created automatically');
    }
    
    // Check if user already exists
    const existingUser = await mongoose.connection.db.collection('users').findOne({ email });
    
    if (existingUser) {
      console.log(`User with email ${email} already exists. Updating password...`);
      
      // Hash the password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(password, salt);
      
      // Update the user
      await mongoose.connection.db.collection('users').updateOne(
        { email },
        { 
          $set: { 
            password: hashedPassword,
            name: name || existingUser.name
          } 
        }
      );
      
      console.log('✅ User updated successfully!');
    } else {
      console.log(`Creating new user with email: ${email}`);
      
      // Hash the password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(password, salt);
      
      // Create default preferences
      const preferences = {
        timeInterval: '60',
        startHour: '0',
        endHour: '23',
        timeFormat: '24',
        darkMode: false,
        syncEnabled: true,
        emailNotifications: true
      };
      
      // Create the user
      const result = await mongoose.connection.db.collection('users').insertOne({
        name,
        email,
        password: hashedPassword,
        preferences,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      console.log('✅ User created successfully!');
      console.log(`- ID: ${result.insertedId}`);
    }
    
    // Verify the user exists
    const user = await mongoose.connection.db.collection('users').findOne({ email });
    console.log('\nUser in database:');
    console.log(`- ID: ${user._id}`);
    console.log(`- Name: ${user.name}`);
    console.log(`- Email: ${user.email}`);
    console.log(`- Password hash length: ${user.password.length}`);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    // Close the connection
    if (mongoose.connection.readyState !== 0) {
      await mongoose.disconnect();
      console.log('\nDisconnected from MongoDB');
    }
  }
}

// Get email, password, and name from command line arguments
const email = process.argv[2];
const password = process.argv[3];
const name = process.argv[4] || 'Test User';

if (!email || !password) {
  console.error('Please provide email and password as arguments:');
  console.error('node src/scripts/create-test-user.js <email> <password> [name]');
  process.exit(1);
}

// Run the function
createTestUser(email, password, name);
