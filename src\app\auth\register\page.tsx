"use client"

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { LayoutDashboard, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

// No schema needed for registration - system generates access key automatically
const registerSchema = z.object({});

export default function RegisterPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [generatedKey, setGeneratedKey] = useState<string | null>(null);
  const router = useRouter();

  const form = useForm<z.infer<typeof registerSchema>>({
    resolver: zodResolver(registerSchema),
    defaultValues: {},
  });

  async function onSubmit(values: z.infer<typeof registerSchema>) {
    setIsLoading(true);

    try {
      console.log('Generating new access key...');

      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}),
      });

      const data = await response.json();
      console.log('Registration response:', { status: response.status, data });

      if (!response.ok) {
        // Check if error is in the expected format
        const errorMessage = data.error?.message || data.message || 'Key generation failed';
        throw new Error(errorMessage);
      }

      // Extract the generated access key
      const accessKey = data.data?.accessKey;
      if (!accessKey) {
        throw new Error('No access key received from server');
      }

      setGeneratedKey(accessKey);
      toast.success('Access key generated successfully!');
    } catch (error: any) {
      console.error('Key generation error:', error);
      toast.error(error.message || 'Key generation failed');
    } finally {
      setIsLoading(false);
    }
  }

  const copyToClipboard = async () => {
    if (generatedKey) {
      try {
        await navigator.clipboard.writeText(generatedKey);
        toast.success('Access key copied to clipboard!');
      } catch (error) {
        toast.error('Failed to copy to clipboard');
      }
    }
  };

  const goToLogin = () => {
    router.push('/auth/login');
  };

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <Link href="/" className="absolute left-4 top-4 md:left-8 md:top-8 flex items-center gap-2 font-bold">
        <LayoutDashboard className="h-5 w-5" />
        <span>NoteHour</span>
      </Link>

      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl">
            {generatedKey ? 'Your Access Key' : 'Generate Access Key'}
          </CardTitle>
          <CardDescription>
            {generatedKey
              ? 'Save this key safely - you\'ll need it to access your account'
              : 'Generate a unique access key for your account'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          {generatedKey ? (
            <div className="space-y-4">
              <div className="p-4 bg-muted rounded-lg">
                <div className="text-sm font-medium mb-2">Your Access Key:</div>
                <div className="font-mono text-lg font-bold text-center p-3 bg-background rounded border-2 border-primary">
                  {generatedKey}
                </div>
              </div>

              <div className="text-sm text-muted-foreground space-y-2">
                <p>⚠️ <strong>Important:</strong> Save this key safely!</p>
                <ul className="list-disc list-inside space-y-1 text-xs">
                  <li>This key is your only way to access your account</li>
                  <li>We don't store your email or personal information</li>
                  <li>If you lose this key, you'll lose access to your account</li>
                </ul>
              </div>

              <div className="flex gap-2">
                <Button onClick={copyToClipboard} variant="outline" className="flex-1">
                  Copy Key
                </Button>
                <Button onClick={goToLogin} className="flex-1">
                  Continue to Login
                </Button>
              </div>
            </div>
          ) : (
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <div className="text-sm text-muted-foreground space-y-2">
                  <p>🔐 <strong>Privacy-focused authentication:</strong></p>
                  <ul className="list-disc list-inside space-y-1 text-xs">
                    <li>No email or password required</li>
                    <li>System generates a unique, memorable key</li>
                    <li>Your data stays private</li>
                  </ul>
                </div>

                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Generating Key...
                    </>
                ) : (
                  'Generate Access Key'
                )}
                </Button>
              </form>
            </Form>
          )}
        </CardContent>
        <CardFooter className="flex flex-col items-center gap-2">
          {!generatedKey && (
            <div className="text-sm text-muted-foreground">
              Already have an access key?{' '}
              <Link href="/auth/login" className="font-medium text-primary underline-offset-4 hover:underline">
                Sign In
              </Link>
            </div>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}
