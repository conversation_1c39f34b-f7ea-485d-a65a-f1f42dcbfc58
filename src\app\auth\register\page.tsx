"use client"

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { LayoutDashboard, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

// No schema needed for registration - system generates access key automatically
const registerSchema = z.object({});

export default function RegisterPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [generatedKey, setGeneratedKey] = useState<string | null>(null);
  const [userName, setUserName] = useState('');
  const [isRegistered, setIsRegistered] = useState(false);
  const router = useRouter();

  const form = useForm<z.infer<typeof registerSchema>>({
    resolver: zodResolver(registerSchema),
    defaultValues: {},
  });

  async function generateNewKey() {
    setIsLoading(true);

    try {
      console.log('Generating new access key...');

      const response = await fetch('/api/auth/generate-key', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      console.log('Key generation response:', { status: response.status, data });

      if (!response.ok) {
        const errorMessage = data.error?.message || data.message || 'Key generation failed';
        throw new Error(errorMessage);
      }

      const accessKey = data.data?.accessKey;
      if (!accessKey) {
        throw new Error('No access key received from server');
      }

      setGeneratedKey(accessKey);
      setIsRegistered(false); // Reset registration status
      toast.success('New access key generated!');
    } catch (error: any) {
      console.error('Key generation error:', error);
      toast.error(error.message || 'Failed to generate access key');
    } finally {
      setIsLoading(false);
    }
  }

  async function saveKey() {
    if (!generatedKey) return;

    setIsSaving(true);

    try {
      console.log('Saving access key...');

      const response = await fetch('/api/auth/save-key', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          accessKey: generatedKey,
          name: userName.trim(),
        }),
      });

      const data = await response.json();
      console.log('Save key response:', { status: response.status, data });

      if (!response.ok) {
        const errorMessage = data.error?.message || data.message || 'Failed to save access key';
        throw new Error(errorMessage);
      }

      setIsRegistered(true);
      toast.success('Account created successfully! You can now log in.');
    } catch (error: any) {
      console.error('Save key error:', error);
      toast.error(error.message || 'Failed to save access key');
    } finally {
      setIsSaving(false);
    }
  }

  async function onSubmit(values: z.infer<typeof registerSchema>) {
    generateNewKey();
  }

  const copyToClipboard = async () => {
    if (generatedKey) {
      try {
        await navigator.clipboard.writeText(generatedKey);
        toast.success('Access key copied to clipboard!');
      } catch (error) {
        toast.error('Failed to copy to clipboard');
      }
    }
  };

  const goToLogin = () => {
    router.push('/auth/login');
  };

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <Link href="/" className="absolute left-4 top-4 md:left-8 md:top-8 flex items-center gap-2 font-bold">
        <LayoutDashboard className="h-5 w-5" />
        <span>NoteHour</span>
      </Link>

      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl">
            {isRegistered ? 'Account Created!' : generatedKey ? 'Your Access Key' : 'Generate Access Key'}
          </CardTitle>
          <CardDescription>
            {isRegistered
              ? 'Your account has been created successfully'
              : generatedKey
              ? 'Like this key? Add your name and save it to create your account'
              : 'Generate unique access keys until you find one you like'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isRegistered ? (
            <div className="space-y-4">
              <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                <div className="text-sm font-medium mb-2 text-green-800 dark:text-green-200">Account Created Successfully!</div>
                <div className="font-mono text-lg font-bold text-center p-3 bg-background rounded border-2 border-green-500">
                  {generatedKey}
                </div>
                <div className="text-xs text-green-600 dark:text-green-400 mt-2 text-center">
                  ✅ This key has been saved to your account
                </div>
              </div>

              <div className="text-sm text-muted-foreground space-y-2">
                <p>🎉 <strong>Welcome to NoteHour!</strong></p>
                <ul className="list-disc list-inside space-y-1 text-xs">
                  <li>Your account is ready to use</li>
                  <li>Keep your access key safe</li>
                  <li>You can now log in and start organizing your time</li>
                </ul>
              </div>

              <div className="flex gap-2">
                <Button onClick={copyToClipboard} variant="outline" className="flex-1">
                  Copy Key
                </Button>
                <Button onClick={goToLogin} className="flex-1">
                  Go to Login
                </Button>
              </div>
            </div>
          ) : generatedKey ? (
            <div className="space-y-4">
              <div className="p-4 bg-muted rounded-lg">
                <div className="text-sm font-medium mb-2">Generated Access Key:</div>
                <div className="font-mono text-lg font-bold text-center p-3 bg-background rounded border-2 border-primary">
                  {generatedKey}
                </div>
              </div>

              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium mb-2 block">Your Name (Optional)</label>
                  <Input
                    type="text"
                    placeholder="Enter your name"
                    value={userName}
                    onChange={(e) => setUserName(e.target.value)}
                    maxLength={50}
                    className="w-full"
                  />
                  <div className="text-xs text-muted-foreground mt-1">
                    This will be displayed in your profile and navbar
                  </div>
                </div>
              </div>

              <div className="text-sm text-muted-foreground space-y-2">
                <p>💡 <strong>Like this key?</strong></p>
                <ul className="list-disc list-inside space-y-1 text-xs">
                  <li>Save it to create your account</li>
                  <li>Or generate a new one if you prefer</li>
                  <li>You can always regenerate keys later</li>
                </ul>
              </div>

              <div className="flex gap-2">
                <Button onClick={generateNewKey} variant="outline" className="flex-1" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    'Generate New Key'
                  )}
                </Button>
                <Button onClick={saveKey} className="flex-1" disabled={isSaving}>
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    'Save & Create Account'
                  )}
                </Button>
              </div>
            </div>
          ) : (
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <div className="text-sm text-muted-foreground space-y-2">
                  <p>🔐 <strong>Privacy-focused authentication:</strong></p>
                  <ul className="list-disc list-inside space-y-1 text-xs">
                    <li>No email or password required</li>
                    <li>System generates a unique, memorable key</li>
                    <li>Your data stays private</li>
                  </ul>
                </div>

                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Generating Key...
                    </>
                ) : (
                  'Generate Access Key'
                )}
                </Button>
              </form>
            </Form>
          )}
        </CardContent>
        <CardFooter className="flex flex-col items-center gap-2">
          {!generatedKey && (
            <div className="text-sm text-muted-foreground">
              Already have an access key?{' '}
              <Link href="/auth/login" className="font-medium text-primary underline-offset-4 hover:underline">
                Sign In
              </Link>
            </div>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}
