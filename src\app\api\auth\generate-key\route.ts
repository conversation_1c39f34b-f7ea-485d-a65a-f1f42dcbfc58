import { NextRequest } from 'next/server';
import { generateAccessKey, isValidAccessKeyFormat } from '@/utils/auth';
import { userRepository } from '@/core/repositories';
import { successResponse, errorResponse } from '@/utils/response';
import { ErrorCode } from '@/constants/error-codes';
import logger from '@/utils/logger';

/**
 * POST /api/auth/generate-key - Generate a new access key without saving to database
 */
export async function POST(req: NextRequest) {
  try {
    logger.info('Generating new access key without saving...');

    // Generate a unique access key
    let accessKey: string;
    let attempts = 0;
    const maxAttempts = 10;

    do {
      accessKey = generateAccessKey();
      attempts++;

      if (attempts > maxAttempts) {
        return errorResponse(
          'Failed to generate unique access key',
          ErrorCode.INTERNAL_SERVER_ERROR,
          500
        );
      }
    } while (await userRepository.accessKeyExists(accessKey));

    logger.info(`Generated unique access key: ${accessKey}`);

    return successResponse({
      accessKey: accessKey,
      message: 'Access key generated successfully'
    });
  } catch (error) {
    logger.error('Key generation error:', error);
    return errorResponse(
      'An unexpected error occurred',
      ErrorCode.INTERNAL_SERVER_ERROR,
      500
    );
  }
}
