import mongoose from 'mongoose';

/**
 * User schema
 */
export const userSchema = new mongoose.Schema({
  accessKey: {
    type: String,
    required: [true, 'Access key is required'],
    unique: true,
    trim: true,
    lowercase: true,
    index: true,
  },
  preferences: {
    timeInterval: {
      type: String,
      enum: ['15', '30', '60', '120', '180', '240', '300', '360', '420', '480', '540', '600', '660', '720'],
      default: '60',
    },
    startHour: {
      type: String,
      default: '0',
    },
    endHour: {
      type: String,
      default: '23',
    },
    timeFormat: {
      type: String,
      enum: ['12', '24'],
      default: '12',
    },
    darkMode: {
      type: Boolean,
      default: false,
    },
    syncEnabled: {
      type: Boolean,
      default: true,
    },
    emailNotifications: {
      type: Boolean,
      default: true,
    },
    customTimeBlocks: [{
      startTime: {
        type: String,
        required: true,
        match: [/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Start time must be in format HH:MM'],
      },
      endTime: {
        type: String,
        required: true,
        match: [/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'End time must be in format HH:MM'],
      }
    }],
    useCustomTimeBlocks: {
      type: Boolean,
      default: false,
    },

  },
}, {
  timestamps: true,
});


