"use client"

import React, { useState, useEffect, useRef, lazy, Suspense } from 'react';
import { parseISO, isToday, format } from 'date-fns';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import { TimeBlock } from '@/lib/types';
import { useTimeBlocks } from '@/hooks/use-time-blocks';
import { usePreferences } from '@/hooks/use-preferences';
import { Loader2 } from 'lucide-react';

// Import components
import { HomeHeader } from './components';

// Dynamic imports for better performance
const GridViewTab = lazy(() => import('./components/GridViewTab').then(module => ({ default: module.GridViewTab })));
const TimeBlockFormWrapper = lazy(() => import('./components/TimeBlockFormWrapper').then(module => ({ default: module.TimeBlockFormWrapper })));
const TodoTab = lazy(() => import('./components/TodoTab').then(module => ({ default: module.TodoTab })));

export default function HomePage() {
  const [selectedDate] = useState<Date>(new Date());
  const [activeTab, setActiveTab] = useState("grid");
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [blockToEdit, setBlockToEdit] = useState<TimeBlock | undefined>(undefined);
  const { timeBlocks, loading, addTimeBlock, updateTimeBlock, deleteTimeBlock, refreshTimeBlocks } = useTimeBlocks();
  const [filteredBlocks, setFilteredBlocks] = useState<TimeBlock[]>([]);
  const [allTodos, setAllTodos] = useState<TimeBlock[]>([]);
  const [timeInterval, setTimeInterval] = useState<string>("60");
  const { preferences } = usePreferences();
  const [convertingRoutines, setConvertingRoutines] = useState(false);

  // Update time interval from preferences
  useEffect(() => {
    if (preferences?.timeInterval) {
      setTimeInterval(preferences.timeInterval);
    }
  }, [preferences]);

  // Convert routines to todos when the page loads or when returning to this page
  // Use a ref to track if we've already done the initial load
  const initialLoadDoneRef = React.useRef(false);

  useEffect(() => {
    // We'll use a timestamp to prevent too frequent conversions
    const lastConversionTime = sessionStorage.getItem('lastRoutineConversionTime');
    const now = Date.now();
    const timeSinceLastConversion = lastConversionTime ? now - parseInt(lastConversionTime) : Infinity;

    // Only run if it's been at least 30 seconds since the last conversion
    // This prevents excessive API calls while still ensuring timely updates
    const shouldRunConversion = timeSinceLastConversion > 30000;

    // If we've already done the initial load and we shouldn't run conversion, just return
    if (initialLoadDoneRef.current && !shouldRunConversion) {
      return;
    }

    const convertRoutinesToTodos = async () => {
      // If we're already converting, don't start another conversion
      if (convertingRoutines) {
        return;
      }

      try {
        setConvertingRoutines(true);

        // Only make the API call if we should run conversion
        if (shouldRunConversion) {
          const response = await fetch('/api/routines/convert-to-todos', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache',
              'Expires': '0'
            },
            credentials: 'include'
          });

          if (response.ok) {
            // Store the current timestamp as the last conversion time
            sessionStorage.setItem('lastRoutineConversionTime', now.toString());
          }
        }

        // Always refresh time blocks after conversion to ensure UI is up to date
        // But only do it once per page load
        if (!initialLoadDoneRef.current) {
          await refreshTimeBlocks(true);
          initialLoadDoneRef.current = true;
        }
      } catch (error) {
        console.error('Error converting routines to todos:', error);
      } finally {
        setConvertingRoutines(false);
      }
    };

    // Small delay to prevent this from running immediately on page load
    // This helps prevent race conditions with other initialization code
    const timer = setTimeout(() => {
      convertRoutinesToTodos();
    }, 300);

    return () => clearTimeout(timer);
  }, [refreshTimeBlocks, convertingRoutines]);

  useEffect(() => {
    if (timeBlocks) {
      // Format the selected date for filtering
      const selectedDateStr = format(selectedDate, 'yyyy-MM-dd');

      // Filter blocks for the grid view based on the selected date
      const filtered = timeBlocks.filter(block => {
        // Show time blocks for the selected date
        if (block.date === selectedDateStr) {
          return true;
        }

        // For routine tasks, ensure they appear on the correct date
        if (block.routineId) {
          const blockDate = new Date(block.date);
          const selected = new Date(selectedDate);

          // Only show routine tasks that match the selected date
          if (blockDate.getDate() === selected.getDate() &&
              blockDate.getMonth() === selected.getMonth() &&
              blockDate.getFullYear() === selected.getFullYear()) {
            return true;
          }
        }

        return false;
      });
      setFilteredBlocks(filtered);

      // Get all todos for the todo tab
      // Only show todos for today and future dates (not past dates)
      const today = new Date();
      const todayStr = format(today, 'yyyy-MM-dd');

      const todos = timeBlocks
        .filter(block => {
          if (!block.isTodo) return false;

          const blockDate = parseISO(block.date);
          const blockDateStr = format(blockDate, 'yyyy-MM-dd');

          // Only show todos for today or future dates
          return blockDateStr >= todayStr;
        })
        .sort((a, b) => {
          const dateA = parseISO(a.date);
          const dateB = parseISO(b.date);

          // If one is today and the other isn't, today comes first
          const aTodayStatus = isToday(dateA) ? 0 : 1;
          const bTodayStatus = isToday(dateB) ? 0 : 1;

          if (aTodayStatus !== bTodayStatus) {
            return aTodayStatus - bTodayStatus;
          }

          // For routine todos, they should come first within the same day
          const aIsRoutine = a.note.startsWith('[Routine]') ? 0 : 1;
          const bIsRoutine = b.note.startsWith('[Routine]') ? 0 : 1;

          if (aIsRoutine !== bIsRoutine) {
            return aIsRoutine - bIsRoutine;
          }

          // Otherwise sort by date
          return dateA.getTime() - dateB.getTime();
        });

      setAllTodos(todos);
    }
  }, [timeBlocks, selectedDate]);

  return (
    <div className="min-h-screen bg-background w-full">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex flex-col h-full w-full">
        {/* Header with date display, tabs and add button */}
        <HomeHeader
          selectedDate={selectedDate}
          onAddTimeBlock={() => setIsFormOpen(true)}
          activeTab={activeTab}
        />

        {/* Main content area with proper scrolling */}
        <main className="flex-1 overflow-auto w-full">
          {/* Grid View Tab */}
          <TabsContent 
            value="grid" 
            className="mt-0 data-[state=active]:animate-in data-[state=inactive]:animate-out data-[state=inactive]:fade-out-0 data-[state=active]:fade-in-0 duration-200"
          >
            <div className="w-full px-3 py-3">
              <Suspense fallback={
                <div className="flex items-center justify-center min-h-[200px]">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              }>
                <GridViewTab
                  selectedDate={selectedDate}
                  filteredBlocks={filteredBlocks}
                  onAddBlock={() => setIsFormOpen(true)}
                  onEditBlock={(block) => {
                    setBlockToEdit(block);
                    setIsFormOpen(true);
                  }}
                  onDeleteBlock={deleteTimeBlock}
                  refreshTimeBlocks={refreshTimeBlocks}
                  onUpdateBlock={updateTimeBlock}
                />
              </Suspense>
            </div>
          </TabsContent>

          {/* Todo Tab */}
          <TabsContent 
            value="list" 
            className="mt-0 data-[state=active]:animate-in data-[state=inactive]:animate-out data-[state=inactive]:fade-out-0 data-[state=active]:fade-in-0 duration-200"
          >
            <div className="w-full px-3 py-3">
              <Suspense fallback={
                <div className="flex items-center justify-center min-h-[200px]">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              }>
                <TodoTab
                  selectedDate={selectedDate}
                  onAddBlock={() => setIsFormOpen(true)}
                  onEditBlock={(block: TimeBlock) => {
                    setBlockToEdit(block);
                    setIsFormOpen(true);
                  }}
                  onDeleteBlock={async (id: string) => {
                    try {
                      const success = await deleteTimeBlock(id);
                      if (success) {
                        await refreshTimeBlocks(true);
                      }
                      return success;
                    } catch (error) {
                      console.error("Error deleting time block from todo tab:", error);
                      return false;
                    }
                  }}
                  refreshTimeBlocks={refreshTimeBlocks}
                />
              </Suspense>
            </div>
          </TabsContent>
        </main>

        {/* Time Block Form */}
        <Suspense fallback={null}>
          <TimeBlockFormWrapper
            isOpen={isFormOpen}
            onClose={() => {
              setIsFormOpen(false);
              setBlockToEdit(undefined);
            }}
            timeBlock={blockToEdit}
            selectedDate={selectedDate}
            updateTimeBlock={updateTimeBlock}
            addTimeBlock={addTimeBlock}
            refreshTimeBlocks={refreshTimeBlocks}
            activeTab={activeTab}
          />
        </Suspense>
      </Tabs>
    </div>
  );
}
