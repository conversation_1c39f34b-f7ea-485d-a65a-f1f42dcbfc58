import { NextRequest } from 'next/server';
import { z } from 'zod';
import { authController } from '@/core/controllers';
import { errorResponse } from '@/utils/response';
import { ErrorCode } from '@/constants/error-codes';
import logger from '@/utils/logger';

// Save key request schema
const saveKeySchema = z.object({
  accessKey: z.string()
    .min(1, 'Access key is required')
    .regex(/^[a-z]+-[a-z]+-\d{2}$/, 'Invalid access key format. Expected format: word-word-number'),
  name: z.string().optional().default(''),
});

/**
 * Validate save key request
 */
async function validateSaveKey(req: NextRequest) {
  try {
    const body = await req.json();
    const validatedData = saveKeySchema.parse(body);
    return { data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const firstError = error.errors[0];
      return errorResponse(
        firstError.message,
        ErrorCode.VALIDATION_ERROR,
        400
      );
    }
    return errorResponse(
      'Invalid request data',
      ErrorCode.VALIDATION_ERROR,
      400
    );
  }
}

/**
 * POST /api/auth/save-key - Save a user with a specific access key
 */
export async function POST(req: NextRequest) {
  try {
    // Validate request
    const validationResult = await validateSaveKey(req);
    if ('status' in validationResult) {
      return validationResult;
    }

    // Process registration with specific key
    return authController.registerWithKey(validationResult.data);
  } catch (error) {
    logger.error('Save key error:', error);
    return errorResponse(
      'An unexpected error occurred',
      ErrorCode.INTERNAL_SERVER_ERROR,
      500
    );
  }
}
