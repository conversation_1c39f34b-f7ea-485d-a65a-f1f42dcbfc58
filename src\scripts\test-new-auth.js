/**
 * Test script for the new authentication system
 * Tests key generation, saving, and profile management
 */

const BASE_URL = 'http://localhost:3000';

async function testKeyGeneration() {
  console.log('🔑 Testing key generation...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/auth/generate-key`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Key generation successful:', data.data.accessKey);
      return data.data.accessKey;
    } else {
      console.error('❌ Key generation failed:', data);
      return null;
    }
  } catch (error) {
    console.error('❌ Key generation error:', error);
    return null;
  }
}

async function testSaveKey(accessKey, name = 'Test User') {
  console.log('💾 Testing key saving...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/auth/save-key`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        accessKey: accessKey,
        name: name,
      }),
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Key saving successful:', data.data);
      return true;
    } else {
      console.error('❌ Key saving failed:', data);
      return false;
    }
  } catch (error) {
    console.error('❌ Key saving error:', error);
    return false;
  }
}

async function testLogin(accessKey) {
  console.log('🔐 Testing login...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        accessKey: accessKey,
      }),
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Login successful:', {
        userId: data.data.user.id,
        accessKey: data.data.user.accessKey,
        name: data.data.user.name,
      });
      return data.data.token;
    } else {
      console.error('❌ Login failed:', data);
      return null;
    }
  } catch (error) {
    console.error('❌ Login error:', error);
    return null;
  }
}

async function runTests() {
  console.log('🚀 Starting authentication system tests...\n');
  
  // Test 1: Generate multiple keys
  console.log('Test 1: Generate multiple keys');
  const key1 = await testKeyGeneration();
  const key2 = await testKeyGeneration();
  const key3 = await testKeyGeneration();
  
  if (!key1 || !key2 || !key3) {
    console.error('❌ Key generation tests failed');
    return;
  }
  
  console.log('✅ Generated 3 unique keys:', [key1, key2, key3]);
  console.log('');
  
  // Test 2: Save a key with name
  console.log('Test 2: Save key with name');
  const saved = await testSaveKey(key2, 'John Doe');
  
  if (!saved) {
    console.error('❌ Key saving test failed');
    return;
  }
  
  console.log('');
  
  // Test 3: Try to save the same key again (should fail)
  console.log('Test 3: Try to save duplicate key (should fail)');
  const duplicateSaved = await testSaveKey(key2, 'Jane Doe');
  
  if (duplicateSaved) {
    console.error('❌ Duplicate key was saved (this should not happen)');
    return;
  } else {
    console.log('✅ Duplicate key correctly rejected');
  }
  
  console.log('');
  
  // Test 4: Login with saved key
  console.log('Test 4: Login with saved key');
  const token = await testLogin(key2);
  
  if (!token) {
    console.error('❌ Login test failed');
    return;
  }
  
  console.log('');
  
  // Test 5: Try to login with unsaved key (should fail)
  console.log('Test 5: Try to login with unsaved key (should fail)');
  const invalidLogin = await testLogin(key1);
  
  if (invalidLogin) {
    console.error('❌ Login with unsaved key succeeded (this should not happen)');
    return;
  } else {
    console.log('✅ Login with unsaved key correctly rejected');
  }
  
  console.log('\n🎉 All tests passed! The authentication system is working correctly.');
}

// Run the tests
runTests().catch(console.error);
