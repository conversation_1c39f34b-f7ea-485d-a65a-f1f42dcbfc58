"use client"

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { profileSchema, ProfileFormValues } from '../schemas';
import { Copy, RefreshCw } from 'lucide-react';

interface ProfileFormProps {
  initialData: {
    accessKey: string;
  };
  onProfileUpdate: () => void;
}

export function ProfileForm({ initialData, onProfileUpdate }: ProfileFormProps) {
  const [isRegenerating, setIsRegenerating] = useState(false);

  const form = useForm<ProfileFormValues>({
    resolver: zod<PERSON><PERSON><PERSON>ver(profileSchema),
    defaultValues: {},
  });

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(initialData.accessKey);
      toast.success('Access key copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const regenerateKey = async () => {
    setIsRegenerating(true);

    try {
      const response = await fetch('/api/auth/password', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentAccessKey: initialData.accessKey
        }),
        credentials: 'include',
      });

      const data = await response.json();

      if (!response.ok) {
        const errorMessage = data.error?.message || data.message || 'Failed to regenerate access key';
        throw new Error(errorMessage);
      }

      // Refresh profile data to get the new access key
      onProfileUpdate();

      toast.success('Access key regenerated successfully! Please save your new key.');
    } catch (error: any) {
      console.error('Key regeneration error:', error);
      toast.error(error.message || 'Failed to regenerate access key');
    } finally {
      setIsRegenerating(false);
    }
  };

  return (
    <Card className="shadow-sm">
      <CardContent className="pt-3 px-4">
        <div className="space-y-4">
          <div className="bg-muted/10 p-4 rounded-md">
            <div className="flex items-center justify-between mb-2">
              <label className="text-sm font-medium">Your Access Key</label>
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={copyToClipboard}
                  className="h-7 px-2"
                >
                  <Copy className="h-3 w-3 mr-1" />
                  Copy
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={regenerateKey}
                  disabled={isRegenerating}
                  className="h-7 px-2"
                >
                  <RefreshCw className={`h-3 w-3 mr-1 ${isRegenerating ? 'animate-spin' : ''}`} />
                  {isRegenerating ? 'Regenerating...' : 'Regenerate'}
                </Button>
              </div>
            </div>
            <div className="font-mono text-lg font-bold p-3 bg-background rounded border text-center">
              {initialData.accessKey}
            </div>
            <div className="text-xs text-muted-foreground mt-2">
              ⚠️ This is your unique access key. Keep it safe and don't share it with anyone.
            </div>
          </div>

          <div className="text-sm text-muted-foreground space-y-2">
            <p>🔐 <strong>Privacy-focused account:</strong></p>
            <ul className="list-disc list-inside space-y-1 text-xs">
              <li>No personal information stored</li>
              <li>Access key is your only identifier</li>
              <li>Regenerate your key anytime for security</li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
