"use client"

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { profileSchema, ProfileFormValues } from '../schemas';
import { Copy, RefreshCw, Loader2 } from 'lucide-react';

interface ProfileFormProps {
  initialData: {
    accessKey: string;
    name?: string;
  };
  onProfileUpdate: () => void;
}

export function ProfileForm({ initialData, onProfileUpdate }: ProfileFormProps) {
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const form = useForm<ProfileFormValues>({
    resolver: zod<PERSON><PERSON><PERSON><PERSON>(profileSchema),
    defaultValues: {
      name: initialData.name || '',
    },
  });

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(initialData.accessKey);
      toast.success('Access key copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const saveProfile = async (values: ProfileFormValues) => {
    setIsSaving(true);

    try {
      const response = await fetch('/api/auth/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: values.name,
        }),
        credentials: 'include',
      });

      const data = await response.json();

      if (!response.ok) {
        const errorMessage = data.error?.message || data.message || 'Failed to update profile';
        throw new Error(errorMessage);
      }

      // Refresh profile data
      onProfileUpdate();

      toast.success('Profile updated successfully!');
    } catch (error: any) {
      console.error('Profile update error:', error);
      toast.error(error.message || 'Failed to update profile');
    } finally {
      setIsSaving(false);
    }
  };

  const regenerateKey = async () => {
    setIsRegenerating(true);

    try {
      const response = await fetch('/api/auth/password', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentAccessKey: initialData.accessKey
        }),
        credentials: 'include',
      });

      const data = await response.json();

      if (!response.ok) {
        const errorMessage = data.error?.message || data.message || 'Failed to regenerate access key';
        throw new Error(errorMessage);
      }

      // Refresh profile data to get the new access key
      onProfileUpdate();

      toast.success('Access key regenerated successfully! Please save your new key.');
    } catch (error: any) {
      console.error('Key regeneration error:', error);
      toast.error(error.message || 'Failed to regenerate access key');
    } finally {
      setIsRegenerating(false);
    }
  };

  return (
    <div className="space-y-4">
      {/* Profile Information Form */}
      <Card className="shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Profile Information</CardTitle>
          <CardDescription>
            Manage your profile details and account settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(saveProfile)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Display Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter your name (optional)"
                        {...field}
                        maxLength={50}
                      />
                    </FormControl>
                    <FormMessage />
                    <div className="text-xs text-muted-foreground">
                      This name will be displayed in your navbar and profile
                    </div>
                  </FormItem>
                )}
              />

              <Button type="submit" disabled={isSaving} className="w-full">
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  'Save Profile'
                )}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>

      {/* Access Key Management */}
      <Card className="shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Access Key</CardTitle>
          <CardDescription>
            Your unique access key for authentication
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="bg-muted/10 p-4 rounded-md">
              <div className="flex items-center justify-between mb-2">
                <label className="text-sm font-medium">Your Access Key</label>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={copyToClipboard}
                    className="h-7 px-2"
                  >
                    <Copy className="h-3 w-3 mr-1" />
                    Copy
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={regenerateKey}
                    disabled={isRegenerating}
                    className="h-7 px-2"
                  >
                    <RefreshCw className={`h-3 w-3 mr-1 ${isRegenerating ? 'animate-spin' : ''}`} />
                    {isRegenerating ? 'Regenerating...' : 'Regenerate'}
                  </Button>
                </div>
              </div>
              <div className="font-mono text-lg font-bold p-3 bg-background rounded border text-center">
                {initialData.accessKey}
              </div>
              <div className="text-xs text-muted-foreground mt-2">
                ⚠️ This is your unique access key. Keep it safe and don't share it with anyone.
              </div>
            </div>

            <div className="text-sm text-muted-foreground space-y-2">
              <p>🔐 <strong>Privacy-focused account:</strong></p>
              <ul className="list-disc list-inside space-y-1 text-xs">
                <li>No personal information stored beyond what you choose to add</li>
                <li>Access key is your primary identifier</li>
                <li>Regenerate your key anytime for security</li>
                <li>New keys are immediately saved to your account</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
