import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// This function can be marked `async` if using `await` inside
export function middleware(request: NextRequest) {
  // Get the pathname of the request
  const path = request.nextUrl.pathname;

  // Define public paths that don't require authentication
  const isPublicPath =
    path === '/' || // Allow root path without authentication
    path.startsWith('/auth/') ||
    path.startsWith('/demo/') ||
    path === '/api/auth/login' ||
    path === '/api/auth/register';

  // Check if the user is authenticated
  const token = request.cookies.get('token')?.value;
  const isAuthenticated = !!token;

  // If the path is not public and the user is not authenticated, redirect to landing page
  if (!isPublicPath && !isAuthenticated) {
    return NextResponse.redirect(new URL('/', request.url));
  }

  // If the path is login/register and the user is authenticated, redirect to home page
  if ((path === '/auth/login' || path === '/auth/register') && isAuthenticated) {
    return NextResponse.redirect(new URL('/home', request.url));
  }


  // If user is authenticated and trying to access root, redirect to home
  if (path === '/' && isAuthenticated) {
    return NextResponse.redirect(new URL('/home', request.url));
  }

  // If user is not authenticated and trying to access home, redirect to login
  if (path === '/home' && !isAuthenticated) {
    return NextResponse.redirect(new URL('/auth/login', request.url));
  }

  return NextResponse.next();
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: [
    // Match all paths except for:
    // - API routes that don't need authentication checks
    // - Static files (images, etc)
    // - Favicon
    // - Service worker files
    '/((?!api/auth/login|api/auth/register|_next/static|_next/image|favicon.ico|manifest.json|sw.js|workbox-*.js|service-worker.js|offline.html).*)',
  ],
};
