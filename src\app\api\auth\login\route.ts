import { NextRequest } from 'next/server';
import { validate<PERSON><PERSON><PERSON><PERSON> } from '@/app/api/_validators/auth.validator';
import { authController } from '@/core/controllers';
import logger from '@/utils/logger';
import { errorResponse } from '@/utils/response';
import { ErrorCode } from '@/constants/error-codes';

// Tell Next.js this route should always be dynamically rendered
export const dynamic = 'force-dynamic';

/**
 * POST /api/auth/login - Authenticate user with access key
 */
export async function POST(req: NextRequest) {
  try {
    // Validate request
    const validationResult = await validateKeyAuth(req);
    if ('status' in validationResult) {
      return validationResult;
    }

    // Process authentication
    return authController.login(validationResult.data);
  } catch (error) {
    logger.error('Authentication error:', error);
    return errorResponse(
      'An unexpected error occurred',
      ErrorCode.INTERNAL_SERVER_ERROR,
      500
    );
  }
}
