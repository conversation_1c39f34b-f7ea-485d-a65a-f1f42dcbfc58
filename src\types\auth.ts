/**
 * JWT payload interface
 */
export interface JwtPayload {
  id: string;
  accessKey: string;
}

/**
 * Authentication result interface
 */
export interface AuthResult {
  authenticated: boolean;
  user?: JwtPayload;
}

/**
 * Key authentication request interface
 */
export interface KeyAuthRequest {
  accessKey: string;
}

/**
 * Register request interface (generates new access key)
 */
export interface RegisterRequest {
  // No fields needed - system generates access key automatically
}

/**
 * Key regeneration request interface
 */
export interface KeyRegenerationRequest {
  currentAccessKey: string;
}

/**
 * User interface (simplified for frontend use)
 */
export interface User {
  id: string;
  accessKey: string;
  name?: string;
  createdAt?: string;
}

/**
 * User profile interface
 */
export interface UserProfile {
  id: string;
  accessKey: string;
  name?: string;
  preferences: {
    timeInterval: string;
    startHour: string;
    endHour: string;
    timeFormat: string;
    darkMode: boolean;
    syncEnabled: boolean;
    emailNotifications: boolean;
  };
}
