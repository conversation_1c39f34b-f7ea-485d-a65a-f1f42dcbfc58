"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from '@/components/ui/tabs';
import { useProfile } from '@/hooks/use-profile';
import { usePreferences } from '@/hooks/use-preferences';
import { ProfileForm, PasswordForm, PreferencesForm } from './components';
import { NotesDownloadForm } from './components/NotesDownloadForm';
import { PreferencesFormValues } from './schemas';

export default function SettingsPage() {
  // Use our improved hooks
  const { profile, refreshProfile } = useProfile();
  const { preferences, savePreferences, refreshPreferences } = usePreferences();

  console.log('SettingsPage: Current preferences:', preferences);

  // Function to handle saving preferences
  const handleSavePreferences = async (values: PreferencesFormValues) => {
    console.log('SettingsPage: Saving preferences:', values);
    const result = await savePreferences(values);
    if (result) {
      console.log('SettingsPage: Preferences saved successfully, refreshing...');
      // Refresh both profile and preferences to ensure consistency
      await Promise.all([
        refreshProfile(true),
        refreshPreferences(true)
      ]);
    }
    return result;
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="px-2 sm:px-4 md:px-6 py-2 space-y-2">
        {/* Settings header */}
      

        {/* Settings content */}
        <div className="bg-card rounded-lg shadow-sm">
          <Tabs defaultValue="profile" className="w-full">
            <TabsList className="w-full flex h-9 p-0.5 bg-background/95">
              <TabsTrigger 
                value="profile" 
                className="flex-1 min-w-[100px] rounded-md text-sm h-8 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
              >
                Profile
              </TabsTrigger>
              <TabsTrigger 
                value="preferences" 
                className="flex-1 min-w-[100px] rounded-md text-sm h-8 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
              >
                Preferences
              </TabsTrigger>
              <TabsTrigger 
                value="download" 
                className="flex-1 min-w-[100px] rounded-md text-sm h-8 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
              >
                Download Notes
              </TabsTrigger>
            </TabsList>

            <div className="p-2 sm:p-3">
              <TabsContent value="profile" className="mt-0 space-y-3">
                <ProfileForm
                  initialData={{
                    accessKey: profile?.accessKey || '',
                    name: profile?.name || '',
                  }}
                  onProfileUpdate={refreshProfile}
                />
              </TabsContent>

              <TabsContent value="preferences" className="mt-0">
                {/* Preferences Form Component */}
                <div className="bg-card rounded-lg border p-2 sm:p-3">
                  <h2 className="text-sm font-medium mb-2">User Preferences</h2>
                  <PreferencesForm
                    initialData={{
                      timeInterval: (preferences?.timeInterval || '60') as any,
                      startHour: preferences?.startHour || '0',
                      endHour: preferences?.endHour || '23',
                      timeFormat: (preferences?.timeFormat || '24') as any,
                      darkMode: preferences?.darkMode || false,
                      syncEnabled: preferences?.syncEnabled !== undefined ? preferences.syncEnabled : false,
                      customTimeBlocks: preferences?.customTimeBlocks || [],
                      useCustomTimeBlocks: preferences?.useCustomTimeBlocks || false,
                    }}
                    onSavePreferences={handleSavePreferences}
                  />
                </div>
              </TabsContent>

              <TabsContent value="download" className="mt-0">
                <div className="bg-card rounded-lg border p-2 sm:p-3">
                  <h2 className="text-sm font-medium mb-2">Download Your Notes</h2>
                  <NotesDownloadForm />
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </div>
    </div>
  );
}