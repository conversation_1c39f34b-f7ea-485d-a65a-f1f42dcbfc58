import * as z from 'zod';

export const profileSchema = z.object({
  // No editable fields for profile - access key is read-only
});

export const keyRegenerationSchema = z.object({
  currentAccessKey: z.string()
    .min(1, { message: 'Current access key is required' })
    .regex(/^[a-z]+-[a-z]+-\d{2}$/, { message: 'Invalid access key format' }),
});

const customTimeBlockSchema = z.object({
  startTime: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, {
    message: "Start time must be in 24-hour format (HH:MM)",
  }),
  endTime: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, {
    message: "End time must be in 24-hour format (HH:MM)",
  }),
}).refine(data => {
  const start = data.startTime.split(':').map(Number);
  const end = data.endTime.split(':').map(Number);
  const startMinutes = start[0] * 60 + start[1];
  const endMinutes = end[0] * 60 + end[1];
  // Allow overnight time blocks (end time can be next day)
  return endMinutes > startMinutes || endMinutes === 0;
}, {
  message: "End time must be after start time (or 00:00 for next day)",
  path: ["endTime"],
});

export const preferencesSchema = z.object({
  timeInterval: z.enum(['15', '30', '60', '120', '180', '240', '300', '360', '420', '480', '540', '600', '660', '720'], {
    required_error: 'You need to select a time interval',
  }),
  startHour: z.string().regex(/^([01]?[0-9]|2[0-3])$/, {
    message: "Start hour must be between 0-23",
  }),
  endHour: z.string().regex(/^([01]?[0-9]|2[0-3])$/, {
    message: "End hour must be between 0-23",
  }),
  timeFormat: z.enum(['12', '24'], {
    required_error: 'You need to select a time format',
  }),
  darkMode: z.boolean().default(false),
  syncEnabled: z.boolean().default(true), // Enable sync by default
  customTimeBlocks: z.array(customTimeBlockSchema).optional().default([]),
  useCustomTimeBlocks: z.boolean().default(false),
});

export type ProfileFormValues = z.infer<typeof profileSchema>;
export type KeyRegenerationFormValues = z.infer<typeof keyRegenerationSchema>;
export type PreferencesFormValues = z.infer<typeof preferencesSchema>;
